<p-sidebar
  [visible]="isVisibleRequestPanel()"
  (visibleChange)="closeSideBar($event)"
  position="right"
  [style]="{ width: '400px', backgroundColor: 'white' }"
>
  <ng-template pTemplate="header">
    <div class="fs-20 f-bold">{{ editMode ? 'Edit' : 'Add' }} Request</div>
  </ng-template>

  <div class="fs-14 fw-400">
    <div class="mb-12">
      <p>Complete the below section for your request sailing.</p>
    </div>
    <p-tabView
      styleClass="border-bottom pb-22"
      [(activeIndex)]="selectedTabIndex"
      (activeIndexChange)="onTabChange($event)"
    >
      <p-tabPanel header="Vessel info" class="mb-24">
        <div class="w-100">
          <form [formGroup]="form" class="d-flex gap-16">
            <div class="flex-1 d-flex flex-direction-column gap-8">
              <div class="fs-14 d-flex align-items-center gap-8 mt-12">
                <img
                  src="assets/arrow.svg"
                  alt="users-plus"
                  style="margin-right: 4px; height: 20px; width: 20px"
                />
                <b>{{ headerText }}</b>
              </div>

              <div class="d-flex flex-direction-column gap-4 mt-12">
                <p class="fs-14 d-flex flex-direction-column gap-4">
                  Select cluster to create a request.
                </p>
                <div class="d-flex flex-direction-column gap-4">
                  <p-dropdown
                    [options]="clusterAssets"
                    formControlName="clusterId"
                    optionValue="assetId"
                    optionLabel="name"
                    [showClear]="true"
                    [filter]="true"
                    [placeholder]="hasCompletedRequests ? '' : 'Add Cluster'"
                    appendTo="body"
                    (onChange)="onClusterChange($event.value)"
                    (onClear)="clearClusterSelection()"
                    styleClass="new-version"
                    [disabled]="hasCompletedRequests"
                    panelStyleClass="new-version-panel"
                  >
                  </p-dropdown>
                </div>
              </div>

              <div class="d-flex flex-direction-column gap-4 mt-12">
                <div class="d-flex align-content-center">
                  <b>
                    Offshore Installations <span style="color: red">*</span>
                  </b>
                </div>
                <p class="fs-14 d-flex flex-direction-column gap-4">
                  Only select the offshore installations to be visited
                </p>
                <div class="mb-24">
                  <p-multiSelect
                    [options]="filteredOffshoreInstallations"
                    formControlName="sailingRequestAssets"
                    optionValue="assetId"
                    optionLabel="name"
                    [showClear]="true"
                    [filter]="true"
                    [placeholder]="
                      hasCompletedRequests
                        ? 'Installations locked (TR completed)'
                        : 'Offshore installations'
                    "
                    appendTo="body"
                    (onChange)="onInstallationSelectionChange()"
                    (onClear)="clearInstallationSelection()"
                    styleClass="new-version-multiselect"
                    [disabled]="hasCompletedRequests"
                    panelStyleClass="new-version-panel"
                  />
                  <small
                    class="validation-control-error"
                    *ngIf="
                      form.get('sailingRequestAssets')?.invalid &&
                      form.get('sailingRequestAssets')?.touched
                    "
                  >
                    Offshore installations are required
                  </small>
                </div>
              </div>

              <div class="d-flex flex-direction-column gap-4 mt-12">
                <div class="d-flex align-content-center">
                  <b>Types <span style="color: red">*</span></b>
                </div>
                <p class="fs-14 d-flex flex-direction-column gap-4">
                  Select a direction. Transport request requires a selection
                </p>
                <div class="d-flex mb-16">
                  <p-checkbox
                    formControlName="selectAll"
                    [binary]="true"
                    (onChange)="selectAll($event.checked)"
                    [disabled]="hasCompletedRequests"
                    [label]="'Select all'"
                  ></p-checkbox>
                </div>
                <div class="d-flex">
                  <div class="col ml-8">
                    <button
                      class="toggle-button"
                      style="margin-left: -8px"
                      (click)="inboundSelect()"
                      (mouseenter)="inboundHover = true"
                      (mouseleave)="inboundHover = false"
                      [disabled]="isInboundRequestComplete"
                    >
                      <img
                        *ngIf="!form.controls.isInbound.value && !inboundHover"
                        src="assets/inbound.svg"
                      />
                      <img
                        class="select-image"
                        *ngIf="form.controls.isInbound.value"
                        src="assets/inbound-selected-active.svg"
                      />
                      <img
                        class="select-image"
                        *ngIf="!form.controls.isInbound.value && inboundHover"
                        src="assets/inbound-hover.svg"
                      />
                    </button>
                  </div>
                  <div class="col ml-8">
                    <button
                      class="toggle-button"
                      (click)="outboundSelect()"
                      (mouseenter)="outboundHover = true"
                      (mouseleave)="outboundHover = false"
                      [disabled]="isOutboundRequestComplete"
                    >
                      <img
                        *ngIf="
                          !form.controls.isOutbound.value && !outboundHover
                        "
                        src="assets/outbound.svg"
                      />
                      <img
                        class="select-image"
                        *ngIf="form.controls.isOutbound.value"
                        src="assets/outbound-selected-active.svg"
                      />
                      <img
                        class="select-image"
                        *ngIf="!form.controls.isOutbound.value && outboundHover"
                        src="assets/outbound-hover.svg"
                      />
                    </button>
                  </div>
                  <div class="col ml-8">
                    <button
                      class="toggle-button"
                      (click)="interfieldSelect()"
                      (mouseenter)="interfieldHover = true"
                      (mouseleave)="interfieldHover = false"
                      [disabled]="isInterfieldRequestComplete"
                    >
                      <img
                        *ngIf="
                          !form.controls.isInterfield.value && !interfieldHover
                        "
                        src="assets/interfield.svg"
                      />
                      <img
                        *ngIf="form.controls.isInterfield.value"
                        src="assets/interfield-selected.svg"
                      />
                      <img
                        *ngIf="
                          !form.controls.isInterfield.value && interfieldHover
                        "
                        src="assets/interfield-hover.svg"
                      />
                    </button>
                  </div>
                </div>
                <small
                  class="validation-control-error"
                  *ngIf="outboundNotSelected()"
                >
                  Interfield request detected, please add an outbound voyage
                </small>
                <small
                  class="validation-control-error"
                  *ngIf="nextButtonClicked && voyageTypeNotSelected()"
                >
                  Type of sailing is required
                </small>
                <small
                  class="validation-control-error"
                  *ngIf="nextButtonClicked && voyageTypeNotSelected()"
                  >Type of sailing is required</small
                >
              </div>
            </div>
          </form>
        </div>
      </p-tabPanel>

      <p-tabPanel
        [disabled]="isRequirementDisabled()"
        header="Requirement"
        class="mb-24"
      >
        <div class="form-section pt-10">
          <request-sailing-requirement
            [activityCategories]="filteredActivities"
            [units]="units ?? []"
            [inboundSelected]="form.controls.isInbound.value ?? false"
            [showErrors]="showErrors"
            [currentUser]="currentUser ?? null"
            [originalClients]="clients ?? []"
            [hasCompletedRequests]="hasCompletedRequests"
            (sendDataToParent)="handleDataFromChild($event)"
          ></request-sailing-requirement>
        </div>
      </p-tabPanel>

      <p-tabPanel
        [disabled]="isApprovalsDisabled()"
        header="Approvals"
        class="mb-24"
      >
        <div class="w-100">
          <request-sailing-approval
            [originalVessels]="vessels"
            [inboundSelection]="form.controls.isInbound.value"
            [outboundSelection]="form.controls.isOutbound.value"
            [showErrors]="showApprovalErrors"
            [hasCompletedRequests]="hasCompletedRequests"
            [isInboundRequestComplete]="isInboundRequestComplete"
            [isOutboundRequestComplete]="isOutboundRequestComplete"
            (approvalDataChanged)="approvalDataChanged($event)"
          ></request-sailing-approval>
        </div>
      </p-tabPanel>

      <p-tabPanel *ngIf="editMode" class="mb-24">
        <ng-template pTemplate="header">
          Comments
          <span *ngIf="addUserToComments" class="red-circle"></span>
        </ng-template>
        <ng-template pTemplate="body">
          <div class="w-100">
            <request-sailing-comments
              [requestSailingId]="requestSailingId"
              [comments]="sailingRequestUserComments"
              (dataChanged)="commentsDataChanged($event)"
            >
            </request-sailing-comments>
          </div>
        </ng-template>
      </p-tabPanel>
    </p-tabView>
  </div>

  <ng-template pTemplate="footer">
    <button class="btn-secondary" (click)="closeSideBar(false)" type="button">
      Cancel
    </button>

    <div
      *ngIf="
        selectedTabIndex === 0 ||
        (selectedTabIndex === 1 && !hasOnlyCliRoleInPlan())
      "
    >
      <button
        class="btn-primary"
        (click)="goToNextTab()"
        [disabled]="
          outboundNotSelected() ||
          !form.get('sailingRequestAssets')?.value?.length ||
          voyageTypeNotSelected() ||
          form.invalid
        "
        type="button"
      >
        Next
      </button>
    </div>
    <div
      *ngIf="
        (selectedTabIndex === 1 && hasOnlyCliRoleInPlan()) ||
        selectedTabIndex === 2
      "
    >
      <button
        [disabled]="loadingCreateEdit()"
        class="save btn-primary d-flex align-items-center"
        (click)="save()"
        type="submit"
      >
        <span [ngStyle]="{ opacity: loadingCreateEdit() ? '0' : '1' }">
          Save
        </span>
        <p-progressSpinner
          class="ml-10"
          [style.position]="'absolute'"
          [styleClass]="'small-spinner-style-btn-white'"
          *ngIf="loadingCreateEdit()"
        ></p-progressSpinner>
      </button>
    </div>
    <div
      *ngIf="editMode && selectedTabIndex === 3 && this.form.value.status === 3"
    >
      <button
        class="btn-negative-primary"
        (click)="deleteSailingRequest()"
        type="button"
      >
        Confirm Delete
      </button>
    </div>
    <div *ngIf="selectedTabIndex === 3">
      <button
        class="btn-primary"
        (click)="allowChildToSendComment()"
        type="button"
      >
        Send
      </button>
    </div>
  </ng-template>
</p-sidebar>
