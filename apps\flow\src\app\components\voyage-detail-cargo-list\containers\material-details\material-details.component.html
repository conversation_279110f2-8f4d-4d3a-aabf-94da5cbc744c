<div
  class="mt-24 mb-28 d-flex justify-content-between"
  [formGroup]="filterForm"
>
  <p-dropdown
    [options]="assetsForFilter()"
    formControlName="assets"
    optionLabel="label"
    optionValue="value"
    placeholder="Select"
    class="ml-16 mr-24"
    styleClass="new-version"
    panelStyleClass="new-version-panel"
    [style.min-width.px]="150"
  />

  <div class="d-flex gap-8">
    <div class="d-flex align-items-center">
      <p-checkbox
        formControlName="isCancelled"
        inputId="isCancelled"
        [binary]="true"
      ></p-checkbox>
      <label for="isCancelled" class="fw-500"
        >Hide Cancelled</label
      >
    </div>
  </div>
</div>

<p-table
  #table
  [value]="filteredMaterialDetailList()"
  [columns]="listColumns()"
  [scrollable]="true"
  [lazy]="true"
  [loading]="tableLoading()"
  styleClass="viewMode"
  scrollHeight="400px"
>
  <ng-template pTemplate="header" let-columns>
    <tr>
      <th
        *ngFor="let column of columns"
        scope="col"
        [style.min-width.px]="column.width"
        [style.width.%]="(column.width / tableWidth()) * 100"
      >
        <span>{{ column.name }}</span>
      </th>
    </tr>
  </ng-template>
  <ng-template
    pTemplate="body"
    let-rowData
    let-index="rowIndex"
    let-columns="columns"
  >
    <tr
      [ngClass]="{
      'cancelled-row': rowData.isCancelled,
    }"
    >
      <ng-container *ngFor="let column of columns">
        <ng-container [ngSwitch]="column.field">
          <td *ngSwitchCase="materialDetailTableFields.rowNumber">
            <div class="d-flex justify-content-between">
              {{ index + 1 }}
            </div>
          </td>

          <td *ngSwitchDefault>
            <ng-container [ngSwitch]="column.fieldType">
              <ng-container *ngSwitchCase="fieldType.dropdown">
                <p-dropdown
                  [options]="dropdownLookups()[column.field]"
                  [ngModel]="rowData[column.field]"
                  optionLabel="label"
                  optionValue="value"
                  placeholder="Select"
                  [showClear]="true"
                  styleClass="new-version"
                  appendTo="body"
                  panelStyleClass="new-version-panel"
                />
              </ng-container>

              <ng-container *ngSwitchCase="fieldType.number">
                <p-inputNumber
                  mode="decimal"
                  [ngModel]="rowData[column.field]"
                  [minFractionDigits]="0"
                  [maxFractionDigits]="column.decimals"
                  placeholder="Enter"
                  [tabindex]="0"
                ></p-inputNumber>
              </ng-container>

              <ng-container *ngSwitchCase="fieldType.timePicker">
                <input
                  pInputText
                  type="text"
                  pInputText
                  [ngModel]="rowData[column.field] | removeSeconds"
                />
              </ng-container>

              <ng-container *ngSwitchDefault>
                <input
                  pInputText
                  type="text"
                  pInputText
                  [ngModel]="rowData[column.field]"
                />
              </ng-container>
            </ng-container>
          </td>

            <td *ngSwitchCase="materialDetailTableFields.voyageMaterialDetailDangerousGoodId">
              <span
                *ngIf="
                  (rowData.voyageCargoCcuId && rowData.voyageMaterialDetailDangerousGoodId) ||
                  (rowData.voyageCargoBulkBulkTypeName && rowData.voyageCargoBulk?.voyageCargoBulkDangerousGood)
                "
                class="d-flex cursor-pointer"
              >
                <img width="25" src="assets/danger.svg" />
              </span>
            </td>

              <td
                  *ngSwitchCase="materialDetailTableFields.properShippingName"
                >
                  {{
                    rowData.voyageMaterialDetailDangerousGood?.voyageCargoDangerousGood
                    ? rowData.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood.dangerousGood.properShippingName
                     : ''
                  }}
                </td>

                <td
                  *ngSwitchCase="materialDetailTableFields.packingGroup"
                >
                  {{
                    rowData.voyageMaterialDetailDangerousGood?.voyageCargoDangerousGood
                    ? rowData.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood.dangerousGood.packingGroup
                     : ''
                  }}
                </td>

                <td
                  *ngSwitchCase="materialDetailTableFields.unNo"
                >
                  {{
                    rowData.voyageMaterialDetailDangerousGood?.voyageCargoDangerousGood
                    ? rowData.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood.dangerousGood.unNo
                     : ''
                  }}
                </td>

                <td
                  *ngSwitchCase="materialDetailTableFields.class"
                >
                  {{
                    rowData.voyageMaterialDetailDangerousGood?.voyageCargoDangerousGood
                    ? rowData.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood.dangerousGood.class
                     : ''
                  }}
                </td>

                <td
                  *ngSwitchCase="materialDetailTableFields.subClass"
                >
                  {{
                    rowData.voyageMaterialDetailDangerousGood?.voyageCargoDangerousGood
                    ? rowData.voyageMaterialDetailDangerousGood.voyageCargoDangerousGood.dangerousGood.subClass
                     : ''
                  }}
                </td>
        </ng-container>
      </ng-container>
    </tr>
  </ng-template>
</p-table>
