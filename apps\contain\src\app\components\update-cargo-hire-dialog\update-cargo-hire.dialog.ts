import {
  ChangeDetectionStrategy,
  Component,
  computed,
  EventEmitter,
  inject,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { DatePipe, NgIf } from '@angular/common';
import { DialogModule } from 'primeng/dialog';
import { FormControl, FormGroup } from '@angular/forms';
import { TableModule } from 'primeng/table';
import { Store } from '@ngrx/store';
import { assetsFeature } from 'libs/services/src/lib/services/maintenance/store/features';
import { HireRequestActions } from 'libs/services/src/lib/services/contain/store/actions/hire-request.actions';
import { ReactiveFormsModule } from '@angular/forms';
import { DropdownModule } from 'primeng/dropdown';
import { HireRequestCargo } from 'libs/services/src/lib/services/contain/interfaces/hire-request-cargo.interface';
import { CalendarModule } from 'primeng/calendar';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { hireRequestFeature } from 'libs/services/src/lib/services/contain/store/features/hire-request.feature';
import { HireRequestCargoEdit } from 'libs/services/src/lib/services/contain/interfaces/hire-request-cargo-edit.interface';
import { stripTimezoneOffset } from 'libs/services/src/lib/services/functions/convert-date.utils';

@Component({
  changeDetection: ChangeDetectionStrategy.OnPush,
  selector: 'contain-update-cargo-hire-dialog',
  standalone: true,
  imports: [
    NgIf,
    DialogModule,
    TableModule,
    ReactiveFormsModule,
    DropdownModule,
    CalendarModule,
    CheckboxModule,
    InputTextModule,
    InputTextareaModule,
    DatePipe,
  ],
  templateUrl: './update-cargo-hire.dialog.html',
  styleUrls: ['./update-cargo-hire.dialog.scss'],
})
export class UpdateCargoHireDialog implements OnInit {
  @Output() dialogToggle = new EventEmitter<void>();
  @Input() dialogVisible: boolean = false;
  @Input() hireRequestCargo!: HireRequestCargo;

  store = inject(Store);

  assets = this.store.selectSignal(assetsFeature.selectAssets);
  filterModel = this.store.selectSignal(hireRequestFeature.selectFilter);

  cargoHireForm: FormGroup = new FormGroup({});

  ngOnInit(): void {
    this.cargoHireForm = this.initialiseForm();
  }

  hasShippedInfo(): boolean {
    return (
      this.hireRequestCargo.shipped !== null &&
      this.hireRequestCargo.manifestOut !== null
    );
  }

  initialiseForm(): FormGroup {
    return new FormGroup({
      offHiredDate: new FormControl<Date | null>(
        this.hireRequestCargo.offHiredDate
          ? new Date(this.hireRequestCargo.offHiredDate)
          : null
      ),
      vendorOutboundDate: new FormControl<Date | null>(
        this.hireRequestCargo.vendorOutboundDate
          ? new Date(this.hireRequestCargo.vendorOutboundDate)
          : null
      ),
      vendorOutbound: new FormControl<string | null>(
        this.hireRequestCargo.vendorOutbound
      ),
      shipped: new FormControl<Date | null>(
        this.hireRequestCargo.shipped
          ? new Date(this.hireRequestCargo.shipped)
          : null
      ),
      manifestOut: new FormControl<string | null>(
        this.hireRequestCargo.manifestOut
      ),
      assetId: new FormControl<string | null>(this.hireRequestCargo.assetId),
      returned: new FormControl<Date | null>(
        this.hireRequestCargo.returned
          ? new Date(this.hireRequestCargo.returned)
          : null
      ),
      manifestIn: new FormControl<string | null>(
        this.hireRequestCargo.manifestIn
      ),
      vendorInbound: new FormControl<string | null>(
        this.hireRequestCargo.vendorInbound
      ),
    });
  }

  submit(): void {
    this.cargoHireForm.markAllAsTouched();

    if (this.cargoHireForm.valid) {
      const model: HireRequestCargoEdit = {
        ...this.cargoHireForm.value,
        clientId: this.hireRequestCargo.clientId,
        shelvesSupplied: this.hireRequestCargo.shelvesSupplied,
        shelvesReturned: this.hireRequestCargo.shelvesReturned,
        netSupplied: this.hireRequestCargo.netSupplied,
        netReturned: this.hireRequestCargo.netReturned,
        tarpaulinSupplied: this.hireRequestCargo.tarpaulinSupplied,
        tarpaulinReturned: this.hireRequestCargo.tarpaulinReturned,
        longTermHire: this.hireRequestCargo.longTermHire,
        billingAssetId: this.hireRequestCargo.billingAssetId,
        vendorId: this.hireRequestCargo.vendorId,
        offHiredDate: this.cargoHireForm.value.offHiredDate
          ? stripTimezoneOffset(this.cargoHireForm.value.offHiredDate)
          : null,
        vendorOutboundDate: this.cargoHireForm.value.vendorOutboundDate
          ? stripTimezoneOffset(this.cargoHireForm.value.vendorOutboundDate)
          : null,
        shipped: this.cargoHireForm.value.shipped
          ? stripTimezoneOffset(this.cargoHireForm.value.shipped)
          : null,
        returned: this.cargoHireForm.value.returned
          ? stripTimezoneOffset(this.cargoHireForm.value.returned)
          : null,
      };

      this.store.dispatch(
        HireRequestActions.update_Cargo_Hire({
          hireRequestCargoId: this.hireRequestCargo.hireRequestCargoId,
          model: model,
          filterModel: this.filterModel(),
        })
      );

      this.hideDialog();
      this.cargoHireForm = this.initialiseForm();
    }
  }

  hideDialog(): void {
    this.dialogToggle.emit();
  }

  minOffHiredDate = computed(() => {
    const returned = this.cargoHireForm.get('returned')?.value;

    if (returned) {
      return new Date(returned);
    }

    return new Date();
  });

  hasValidDatesForOffHiring = computed(() => {
    const returnedDate = this.cargoHireForm.get('returned')?.value;
    const onHiredDate = this.cargoHireForm.get('onHiredDate')?.value;

    if (returnedDate && onHiredDate) {
      return true;
    }

    return false;
  });
}
