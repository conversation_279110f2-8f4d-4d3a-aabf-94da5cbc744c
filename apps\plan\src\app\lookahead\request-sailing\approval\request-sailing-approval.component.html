<div class="container fs-14 fw-400">
  <div class="fs-14 d-flex align-items-center gap-8 mt-12 mb-12">
    <img
      class="mr-4"
      src="assets/arrow.svg"
      alt="users-plus"
      style="height: 20px; width: 20px"
    />
    <b>Request Approval</b>
  </div>
  <p class="fs-14 d-flex flex-direction-column gap-4 mb-12">
    Select a vessel or take action to plan
  </p>
  <form [formGroup]="form">
    <div class="align-items-center d-flex justify-content-start mb-12">
      <div class="col">
        <b>Select vessel</b>
      </div>
    </div>
    <p-dropdown
      [options]="vessels()"
      formControlName="vesselId"
      styleClass="new-version"
      panelStyleClass="new-version-panel"
      optionLabel="name"
      optionValue="vesselId"
      [showClear]="true"
      [filter]="true"
      [disabled]="hasCompletedRequests"
      [placeholder]="hasCompletedRequests ? '' : 'Select Vessel'"
      (onChange)="updateInboundOutbound()"
    >
    </p-dropdown>

    <div class="button-container">
      <div class="d-flex align-items-center justify-content-start mb-12 mt-10">
        <label class="select-voyage fs-14 fw-700">Select Voyage</label>
      </div>
      <div
        class="d-flex align-items-center justify-content-start mb-12 mt-10 add-ons"
      >
        <div
          class="addon-btn mr-16 pt-16 align-items-center d-flex flex-direction-column"
        >
          <div
            class="image-container"
            [ngClass]="{
              disabled:
                !inboundSelection ||
                vesselNotSelectedOrSeriesSelected() ||
                isInboundRequestComplete,
              active: inboundVoyageActive,
              hover: inboundVoyageHover && !isInboundRequestComplete
            }"
            (click)="
              !isInboundRequestComplete && openInboundOutboundSidebar('Inbound')
            "
            (mouseenter)="
              !isInboundRequestComplete && (inboundVoyageHover = true)
            "
            (mouseleave)="inboundVoyageHover = false"
          >
            <img
              *ngIf="!inboundVoyageSelected && !inboundVoyageActive"
              src="assets/inbound.svg"
              class="unchecked"
            />
            <img
              *ngIf="!inboundVoyageSelected && !inboundVoyageActive"
              src="assets/inbound-hover.svg"
              class="hover-image"
            />
            <img
              *ngIf="inboundVoyageSelected && inboundVoyageActive"
              src="assets/inbound-selected-active.svg"
            />
            <img
              *ngIf="inboundVoyageActive && !inboundVoyageSelected"
              src="assets/inbound-hover.svg"
            />
            <img
              *ngIf="!inboundVoyageActive && inboundVoyageSelected"
              src="assets/inbound-selected.svg"
            />
          </div>
        </div>
        <div
          class="col addon-btn mr-16 pt-16 align-items-center d-flex flex-direction-column"
        >
          <div
            class="image-container"
            [ngClass]="{
              disabled:
                !outboundSelection ||
                vesselNotSelectedOrSeriesSelected() ||
                isOutboundRequestComplete,
              active: outboundVoyageActive,
              hover: outboundVoyageHover && !isOutboundRequestComplete
            }"
            (click)="
              !isOutboundRequestComplete &&
                openInboundOutboundSidebar('Outbound')
            "
            (mouseenter)="
              !isOutboundRequestComplete && (outboundVoyageHover = true)
            "
            (mouseleave)="outboundVoyageHover = false"
          >
            <img
              *ngIf="!outboundVoyageSelected && !outboundVoyageActive"
              src="assets/outbound.svg"
              class="unchecked"
            />
            <img
              *ngIf="!outboundVoyageSelected && !outboundVoyageActive"
              src="assets/outbound-hover.svg"
              class="hover-image"
            />
            <img
              *ngIf="outboundVoyageSelected && outboundVoyageActive"
              src="assets/outbound-selected-active.svg"
            />
            <img
              *ngIf="outboundVoyageActive && !outboundVoyageSelected"
              src="assets/outbound-hover.svg"
            />
            <img
              *ngIf="!outboundVoyageActive && outboundVoyageSelected"
              src="assets/outbound-selected.svg"
            />
          </div>
        </div>
      </div>
      <small *ngIf="cannotAssignVoyage()">
        Cannot assign voyage to series
      </small>
    </div>
    <div class="date-time-container fs-16">
      <div
        class="d-flex align-items-center justify-content-start mb-12 mt-10 header-container"
      >
        <div class="fw-400 d-flex align-items-center pb-12">
          <img class="mr-8" src="assets/calendar.svg" alt="users-plus" />
          <span>Select date and time</span>
        </div>
      </div>

      <div class="align-items-center justify-content-start gap-8 mb-8">
        <label class="fs-14 mr-16 pb-16"
          >Start <span class="required">*</span></label
        >
        <p-calendar
          [minDate]="minStartDate"
          [maxDate]="timeAfter!"
          formControlName="startTime"
          (onSelect)="changeStartDate($event)"
          dateFormat="dd/mm/yy"
          placeholder="DD/MM/YYYY"
          [showIcon]="true"
          [readonlyInput]="true"
          [style]="{ width: '100%' }"
        >
        </p-calendar>
        <small
          class="p-error"
          *ngIf="
            form.controls.startTime.hasError('required') &&
            form.controls.startTime.touched
          "
        >
          Start date is required
        </small>
      </div>

      <div class="align-items-center justify-content-start gap-8 mb-8">
        <label class="fs-14 mr-16 pb-16"
          >End <span class="required">*</span></label
        >
        <p-calendar
          [minDate]="timeBefore || today"
          formControlName="endTime"
          (onSelect)="changeEndDate($event)"
          dateFormat="dd/mm/yy"
          placeholder="DD/MM/YYYY"
          [showIcon]="true"
          [readonlyInput]="true"
          [style]="{ width: '100%' }"
        >
        </p-calendar>
        <small
          class="p-error"
          *ngIf="
            form.controls.endTime.hasError('required') &&
            form.controls.endTime.touched
          "
        >
          End date is required
        </small>
      </div>
      <div class="align-items-center justify-content-start gap-8 mb-8">
        <label class="fs-14 mr-16 pb-16"
          >ETA <span class="required" *ngIf="isEtaRequired()">*</span></label
        >
        <p-calendar
          [timeOnly]="true"
          [showIcon]="true"
          [showTime]="true"
          formControlName="eta"
          placeholder="HH:MM"
          dateFormat="HH:MM"
          utc="true"
          [style]="{ width: '100%' }"
        >
        </p-calendar>
        <small
          class="p-error"
          *ngIf="
            form.controls.eta.hasError('required') && form.controls.eta.touched
          "
        >
          ETA is required for Inbound voyages
        </small>
      </div>
      <div class="align-items-center justify-content-start gap-8 mb-12">
        <label class="fs-14 mr-16 pb-16"
          >ETD <span class="required" *ngIf="isEtdRequired()">*</span></label
        >
        <p-calendar
          [timeOnly]="true"
          [showIcon]="true"
          [showTime]="true"
          formControlName="etd"
          placeholder="HH:MM"
          dateFormat="HH:MM"
          utc="true"
          [style]="{ width: '100%' }"
        >
        </p-calendar>

        <small
          class="p-error"
          *ngIf="
            form.controls.etd.hasError('required') && form.controls.etd.touched
          "
        >
          ETD is required for outbound voyages
        </small>
      </div>
      <div
        *ngIf="!inboundOutboundVoyageSelected()"
        class="input-container d-flex align-items-center"
      >
        <p-dropdown
          [ngClass]="
            form.controls.doesRepeat.value === 'DoesRepeat' ? 'select' : ''
          "
          formControlName="doesRepeat"
          [options]="repeatOptions"
          placeholder="Does Not Repeat"
          (onChange)="setDoesRepeat($event.value)"
          styleClass="new-version"
          optionLabel="label"
          optionValue="value"
        ></p-dropdown>
        <button
          pButton
          class="btn-tertiary"
          *ngIf="form.controls.doesRepeat.value === 'DoesRepeat'"
          (click)="openRepeatDialog()"
        >
          <i class="pi pi-pencil" style="color: #a80303"></i>
        </button>
      </div>
      <small
        class="validation-control-error"
        *ngIf="inboundOutboundVoyageSelected()"
        >Cannot create series when voyage is assigned</small
      >
    </div>
    <div class="d-flex align-items-center justify-content-start mt-10">
      <div class="col">
        <h4 class="heading">Add Comment</h4>
      </div>
    </div>
    <div class="date-time-container">
      <div class="header">
        <textarea
          id="comment-box"
          rows="10"
          style="resize: none"
          placeholder="Add Comment..."
          pInputTextarea
          formControlName="comment"
        >
        </textarea>
      </div>
    </div>
    <div
      style="margin-top: 20px"
      class="d-flex align-items-center justify-content-start mb-12 mt-10"
    >
      <div class="col">
        <h4 class="heading">Take Action</h4>
      </div>
    </div>
    <div class="action-buttons">
      <div class="action-container">
        <button
          pButton
          class="action-button delete"
          [ngClass]="{ active: selectedStatus === 3 }"
          [disabled]="hasCompletedRequests"
          (click)="selectStatus(3)"
        >
          <i class="pi pi-times-circle" style="color: #d6002a"></i>
        </button>
        <span>Delete</span>
      </div>
      <div class="action-container">
        <button
          pButton
          class="action-button decline"
          [ngClass]="{ active: selectedStatus === 2 }"
          [disabled]="hasCompletedRequests"
          (click)="selectStatus(2)"
        >
          <i class="pi pi-exclamation-circle" style="color: #a80303"></i>
        </button>
        <span>Decline</span>
      </div>
      <div class="action-container">
        <button
          pButton
          class="action-button approve"
          [ngClass]="{ active: selectedStatus === 1 }"
          [disabled]="hasCompletedRequests"
          (click)="selectStatus(1)"
        >
          <i class="pi pi-check-circle" style="color: #69a803"></i>
        </button>
        <span>Approve</span>
      </div>
    </div>
  </form>
</div>
